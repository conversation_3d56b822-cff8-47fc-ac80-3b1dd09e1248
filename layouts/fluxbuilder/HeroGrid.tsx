import { cn } from '../../lib/utils/cn';
import React, { useState, useEffect, useRef } from "react";

/**
 * InteractiveGridPattern is a component that renders a grid pattern with interactive squares.
 * Now dynamically calculates grid size to fill the entire background with 24px squares.
 *
 * @param width - The width of each square (default: 24px).
 * @param height - The height of each square (default: 24px).
 * @param squares - Optional override for the number of squares. If not provided, calculates based on container size.
 * @param className - The class name of the grid.
 * @param squaresClassName - The class name of the squares.
 */
interface InteractiveGridPatternProps extends React.SVGProps<SVGSVGElement> {
  width?: number;
  height?: number;
  squares?: [number, number]; // [horizontal, vertical] - optional override
  className?: string;
  squaresClassName?: string;
}

/**
 * The InteractiveGridPattern component.
 * Automatically fills the entire background with a grid pattern.
 *
 * @see InteractiveGridPatternProps for the props interface.
 * @returns A React component.
 */
export function InteractiveGridPattern({
  width = 24,
  height = 24,
  squares,
  className,
  squaresClassName,
  ...props
}: InteractiveGridPatternProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const [gridDimensions, setGridDimensions] = useState({ horizontal: 50, vertical: 30 });
  const [hoveredSquare, setHoveredSquare] = useState<number | null>(null);

  // Calculate grid dimensions based on container size
  useEffect(() => {
    const calculateGridSize = () => {
      if (svgRef.current && svgRef.current.parentElement) {
        const container = svgRef.current.parentElement;
        const containerWidth = container.clientWidth || window.innerWidth;
        const containerHeight = container.clientHeight || window.innerHeight;

        // Calculate number of squares needed to fill the container
        const horizontal = Math.ceil(containerWidth / width) + 2; // +2 for buffer
        const vertical = Math.ceil(containerHeight / height) + 2; // +2 for buffer

        setGridDimensions({ horizontal, vertical });
      }
    };

    // Initial calculation
    calculateGridSize();

    // Recalculate on window resize
    window.addEventListener('resize', calculateGridSize);

    // Use ResizeObserver if available for better container size detection
    let resizeObserver: ResizeObserver | null = null;
    if (svgRef.current && svgRef.current.parentElement && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(calculateGridSize);
      resizeObserver.observe(svgRef.current.parentElement);
    }

    return () => {
      window.removeEventListener('resize', calculateGridSize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [width, height]);

  // Use provided squares or calculated dimensions
  const horizontal = squares ? squares[0] : gridDimensions.horizontal;
  const vertical = squares ? squares[1] : gridDimensions.vertical;

  return (
    <svg
      ref={svgRef}
      width={width * horizontal}
      height={height * vertical}
      className={cn(
        "absolute inset-0 h-full w-full pointer-events-auto",
        className,
      )}
      style={{
        minWidth: '100%',
        minHeight: '100%',
      }}
      {...props}
    >
      {Array.from({ length: horizontal * vertical }).map((_, index) => {
        const x = (index % horizontal) * width;
        const y = Math.floor(index / horizontal) * height;
        return (
          <rect
            key={index}
            x={x}
            y={y}
            width={width}
            height={height}
            className={cn(
              "stroke-gray-300/20 fill-transparent transition-all duration-200 ease-in-out cursor-pointer",
              "hover:fill-blue-50/40 hover:stroke-blue-300/30 hover:scale-105",
              hoveredSquare === index ? "fill-blue-50/40 stroke-blue-300/30 scale-105" : "",
              squaresClassName,
            )}
            style={{
              transformOrigin: 'center',
            }}
            onMouseEnter={() => setHoveredSquare(index)}
            onMouseLeave={() => setHoveredSquare(null)}
          />
        );
      })}
    </svg>
  );
}
import Image from 'next/image';
import Link from "next/link";
import { useTranslation } from "../../lib/utils/i18n";
import { memo, useEffect } from 'react';
import { AuroraText } from '@/components/ui/AuroraText';
import { BorderBeam } from '@/components/ui/BorderBeam';

// Optimized SVG components with responsive sizing
const CheckIcon = () => (
  <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
);

const StarIcon = () => (
  <svg className="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
  </svg>
);

const BoltIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
  </svg>
);

// Preload critical images
const preloadedImages = [
  {
    src: '/images/app-builder-demo.png',
    type: 'image/png'
  }
];

// Memoized HeroSection component to prevent unnecessary re-renders
const HeroSection = memo(({ websiteUrl, setWebsiteUrl, handleTryFluxBuilder }) => {
  const { t } = useTranslation();

  // Preload critical images
  useEffect(() => {
    preloadedImages.forEach(({ src, type }) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      if (type) link.type = type;
      document.head.appendChild(link);
    });
  }, []);

  return (
    <section className="relative overflow-hidden bg-white">
      {/* Minimal background with subtle patterns */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808008_1px,transparent_1px),linear-gradient(to_bottom,#80808008_1px,transparent_1px)] bg-[size:24px_24px]" />
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-200/30 to-transparent" />
      </div>

      <div className="container relative mx-auto px-4 py-20 sm:px-6 sm:py-24 lg:px-8 lg:py-28">
        <div className="grid gap-12 lg:grid-cols-2 lg:gap-16 items-center">
          <div className="flex flex-col justify-center max-w-xl">
            {/* Version badge */}
            <div className="flex items-center gap-3 mb-8">
              <div className="flex items-center">
                <span className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-700 bg-blue-50 border border-blue-100 rounded-full">
                  <span className="mr-1.5 relative flex h-1.5 w-1.5">
                    <span className="relative inline-flex w-1.5 h-1.5 bg-blue-600 rounded-full">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-600 opacity-75"></span>
                    </span>
                  </span>
                  {t('footer.new', 'New')}
                </span>
                <span className="ml-3 text-sm text-gray-600">v2.1.1 • Feb 2025</span>
              </div>
            </div>

            {/* Clean headline */}
            <h1 className="text-4xl sm:text-5xl font-bold tracking-tight text-gray-900 md:text-6xl leading-[1.1] mb-6">
              {t('home.hero.title')}{' '}
              <AuroraText speed={0.8}>{t('home.hero.titleHighlight')}</AuroraText>
            </h1>

            {/* Refined subheading */}
            <p className="text-lg sm:text-xl text-gray-600 leading-relaxed mb-8">
              {t('home.hero.subtitle', 'FluxBuilder automates your mobile app creation — handling design, development, and publishing. Try it for free starting today!')}
            </p>

            {/* Clean input form with subtle transitions */}
            <div className="flex w-full max-w-[520px] flex-col gap-6">
              <form onSubmit={handleTryFluxBuilder} className="relative rounded-lg">
                <label htmlFor="website-url" className="sr-only">{t('home.hero.enterWebsite', 'Enter your website URL')}</label>
                <input
                  type="url"
                  id="website-url"
                  name="website-url"
                  placeholder={t('home.hero.enterWebsite', 'Enter your website URL')}
                  className="block w-full px-5 py-4 text-base text-gray-900 bg-gray-50 border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 hover:border-blue-400 transition-colors duration-200 placeholder:text-gray-400"
                  value={websiteUrl}
                  onChange={(e) => setWebsiteUrl(e.target.value)}
                  required
                />
                <button
                  type="submit"
                  className="absolute inline-flex items-center justify-center p-3 text-white -translate-y-1/2 bg-blue-600 right-2 top-1/2 rounded-[9px] hover:bg-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 hover:scale-[1.03]"
                  aria-label={t('home.hero.tryItNow', 'Try It Now')}
                >
                  <ArrowIcon />
                </button>

                <BorderBeam duration={12} size={80} colorTo="#FFD700" colorFrom="#FF6B35" />
              </form>

              {/* Trust indicators */}
              <div className="grid grid-cols-1 xs:grid-cols-2 sm:flex sm:flex-wrap items-start sm:items-center gap-4 sm:gap-6 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <CheckIcon />
                  <span>{t('home.hero.noCreditCard')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckIcon />
                  <span>{t('home.hero.instantPreview')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckIcon />
                  <span>{t('home.hero.iosAndroidSupport')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Clean image display */}
          <div className="relative mt-4 sm:mt-0">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-tr from-gray-50 to-blue-50/30 rounded-2xl -z-10" />
              <div className="p-1 rounded-2xl ">
                <Image
                  src="/images/app-builder-demo.png"
                  alt="FluxBuilder App Interface"
                  width={800}
                  height={600}
                  className="rounded-xl"
                  priority
                  loading="eager"
                  fetchPriority="high"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
                  quality={90}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Trust rating */}
        <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mt-12 sm:mt-16">
          <Link href="/showcase" className="group">
            <div className="flex items-center gap-3 px-4 py-2 bg-white border border-gray-100 rounded-full transition-colors duration-200 hover:border-blue-100">
              <div className="flex items-center gap-[2px]">
                {[1, 2, 3, 4, 5].map((i) => (
                  <StarIcon key={i} />
                ))}
              </div>
              <p className="text-sm font-medium text-gray-600 group-hover:text-blue-600 transition-colors duration-200">
                <span className="font-bold">4.8/5</span> • <span className="hidden xs:inline">{t('home.hero.trustedBy')}</span>
                <span className="xs:hidden">{t('home.hero.userCount')}</span>
              </p>
            </div>
          </Link>
        </div>
      </div>
    </section>
  );
});

HeroSection.displayName = 'HeroSection';

export default HeroSection;
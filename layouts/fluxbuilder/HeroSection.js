import Image from 'next/image';
import Link from "next/link";
import { useTranslation } from "../../lib/utils/i18n";
import { memo } from 'react';
import { AuroraText } from '@/components/ui/AuroraText';
import { BorderBeam } from '@/components/ui/BorderBeam';
import { InteractiveGridPattern } from './HeroGrid';

// Optimized inline SVG components for instant rendering - memoized for performance
const CheckIcon = memo(() => (
  <svg className="w-4 h-4 text-blue-600 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
  </svg>
));

const StarIcon = memo(() => (
  <svg className="w-4 h-4 text-amber-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
  </svg>
));

const BoltIcon = memo(() => (
  <svg className="w-5 h-5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
  </svg>
));

const PlayIcon = memo(() => (
  <svg className="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
  </svg>
));

// Set display names for better debugging
CheckIcon.displayName = 'CheckIcon';
StarIcon.displayName = 'StarIcon';
BoltIcon.displayName = 'BoltIcon';
PlayIcon.displayName = 'PlayIcon';

// Memoized HeroSection component to prevent unnecessary re-renders
const HeroSection = memo(({ websiteUrl, setWebsiteUrl, handleTryFluxBuilder }) => {
  const { t } = useTranslation();

  return (
    <section className="relative min-h-[90vh] flex items-center bg-gradient-to-br from-slate-50 via-white to-blue-50/30 overflow-hidden">
      {/* Modern Interactive Background Grid */}
      <div className="absolute inset-0 z-0">
        <InteractiveGridPattern
          width={24}
          height={24}
          className="opacity-40"
        />
      </div>

      {/* Subtle gradient overlays for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-transparent to-blue-50/20 z-10" />
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-200/40 to-transparent z-10" />

      <div className="container relative mx-auto px-4 py-16 sm:px-6 lg:px-8 z-20">
        <div className="grid gap-16 lg:grid-cols-12 lg:gap-20 items-center">
          <div className="lg:col-span-7 flex flex-col justify-center">
            {/* Modern status badge */}
            <div className="inline-flex items-center gap-2 mb-8 w-fit">
              <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100/50 rounded-full">
                <span className="relative flex h-2 w-2 mr-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-500 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-600"></span>
                </span>
                <span className="text-sm font-semibold text-blue-700">{t('footer.new', 'New')}</span>
              </div>
              <span className="text-sm font-medium text-gray-500">v2.1.1 • Feb 2025</span>
            </div>

            {/* Modern headline with better typography */}
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold tracking-tight text-gray-900 leading-[0.95] mb-6">
              <span className="block">{t('home.hero.title', 'Build Apps')}</span>
              <AuroraText
                speed={0.8}
                className="block bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent"
              >
                {t('home.hero.titleHighlight', 'Instantly')}
              </AuroraText>
            </h1>

            {/* Clean, compelling subheading */}
            <p className="text-xl sm:text-2xl text-gray-600 leading-relaxed mb-10 max-w-2xl">
              {t('home.hero.subtitle', 'Transform any website into a native mobile app in minutes. No coding required.')}
            </p>

            {/* Modern CTA Section */}
            <div className="flex flex-col gap-8 max-w-2xl">
              {/* Primary CTA Form */}
              <div className="relative">
                <form onSubmit={handleTryFluxBuilder} className="relative group">
                  <label htmlFor="website-url" className="sr-only">{t('home.hero.enterWebsite', 'Enter your website URL')}</label>
                  <div className="relative">
                    <input
                      type="url"
                      id="website-url"
                      name="website-url"
                      placeholder={t('home.hero.enterWebsite', 'Enter your website URL (e.g., yoursite.com)')}
                      className="block w-full px-6 py-5 text-lg text-gray-900 bg-white border-2 border-gray-200 rounded-2xl focus:border-blue-500 focus:ring-0 hover:border-gray-300 transition-all duration-300 placeholder:text-gray-400 pr-32"
                      value={websiteUrl}
                      onChange={(e) => setWebsiteUrl(e.target.value)}
                      required
                    />
                    <button
                      type="submit"
                      className="absolute right-2 top-1/2 -translate-y-1/2 inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:scale-105 active:scale-95"
                      aria-label={t('home.hero.tryItNow', 'Try It Now')}
                    >
                      <BoltIcon />
                      <span className="ml-2 hidden sm:inline">{t('home.hero.tryNow', 'Try Now')}</span>
                    </button>
                  </div>
                  <BorderBeam duration={12} size={80} colorTo="#FFD700" colorFrom="#FF6B35" />
                </form>
              </div>

              {/* Secondary CTA */}
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                <Link
                  href="/demo"
                  className="inline-flex items-center gap-2 px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-medium transition-all duration-300 hover:scale-105"
                >
                  <PlayIcon />
                  <span>{t('home.hero.watchDemo', 'Watch Demo')}</span>
                </Link>
                <span className="text-sm text-gray-500">{t('home.hero.freeForever', 'Free forever • No credit card required')}</span>
              </div>

              {/* Trust indicators - Modern design */}
              <div className="flex flex-wrap items-center gap-6 pt-4 border-t border-gray-100">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <CheckIcon />
                  <span className="font-medium">{t('home.hero.instantPreview', 'Instant Preview')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <CheckIcon />
                  <span className="font-medium">{t('home.hero.iosAndroidSupport', 'iOS & Android')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <CheckIcon />
                  <span className="font-medium">{t('home.hero.noCoding', 'No Coding')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Modern Hero Image */}
          <div className="lg:col-span-5 relative">
            <div className="relative group">
              {/* Modern floating effect without shadows */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-100/30 rounded-3xl transform rotate-1 group-hover:rotate-2 transition-transform duration-500" />
              <div className="relative bg-white rounded-3xl border border-gray-200/50 p-2 transform -rotate-1 group-hover:rotate-0 transition-transform duration-500">
                <Image
                  src="/images/app-builder-demo.png"
                  alt="FluxBuilder App Interface - Transform websites into mobile apps instantly"
                  width={800}
                  height={600}
                  className="rounded-2xl w-full h-auto"
                  priority
                  loading="eager"
                  fetchPriority="high"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
                  quality={95}
                />
              </div>

              {/* Floating stats cards */}
              <div className="absolute -bottom-6 -left-6 bg-white rounded-2xl border border-gray-200/50 p-4 backdrop-blur-sm">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckIcon />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-gray-900">10,000+</p>
                    <p className="text-xs text-gray-600">Apps Created</p>
                  </div>
                </div>
              </div>

              <div className="absolute -top-6 -right-6 bg-white rounded-2xl border border-gray-200/50 p-4 backdrop-blur-sm">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <BoltIcon />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-gray-900">&lt; 5 min</p>
                    <p className="text-xs text-gray-600">Setup Time</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Trust Section */}
        <div className="flex flex-col items-center gap-8 mt-20 pt-16 border-t border-gray-100">
          <div className="text-center">
            <p className="text-sm font-medium text-gray-500 mb-4">{t('home.hero.trustedBy', 'Trusted by developers worldwide')}</p>
            <Link href="/showcase" className="group inline-flex items-center gap-4 px-6 py-4 bg-white border border-gray-200 rounded-2xl hover:border-gray-300 transition-all duration-300 hover:scale-105">
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((i) => (
                  <StarIcon key={i} />
                ))}
              </div>
              <div className="text-left">
                <p className="text-lg font-bold text-gray-900">4.8/5</p>
                <p className="text-sm text-gray-600">{t('home.hero.userCount', '2,500+ reviews')}</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
});

HeroSection.displayName = 'HeroSection';

export default HeroSection;
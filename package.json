{"name": "fluxbuilder", "description": "Build a mobile app from your website in minutes", "version": "2.0.0", "private": true, "license": "MIT", "packageManager": "yarn@1.22.22", "engines": {"node": ">=21.6.0"}, "scripts": {"dev": "yarn copy-features && yarn update && next dev", "clean": "rm -rf .next out dist temp", "generate-search-data": "node scripts/generate-search-data.mjs", "copy-features": "node scripts/copy-features.mjs", "build": "yarn clean && yarn generate-search-data && yarn copy-features && NODE_OPTIONS='--max-old-space-size=4096' node build-custom.mjs", "build:next": "yarn clean && yarn generate-search-data && yarn copy-features && NODE_OPTIONS='--max-old-space-size=4096' next build", "deploy": "yarn build && npx wrangler pages deploy dist --branch=production", "deploy:staging": "yarn build && npx wrangler pages deploy dist --branch=staging", "lint": "next lint", "preview": "next start", "json": "node scripts/jsonGenerator.mjs", "screenshot": "node scripts/screenshotThemes.mjs", "sync-showcase": "node scripts/fluxGenerator.mjs --sync", "flux": "node scripts/fluxGenerator.mjs --sync && node scripts/fluxGenerator.mjs", "github": "node scripts/githubGenerator.mjs", "sync-websites": "node scripts/syncAllWebsites.mjs", "update": "node scripts/jsonGenerator.mjs && node scripts/parseNewThemesData.mjs", "update-themes": "yarn json && node scripts/updateThemes.mjs && yarn json", "format": "prettier -w .", "sass:watch": "sass --watch styles:public/styles", "sass:build": "sass styles:public/styles --style compressed", "generate-multilingual-sitemap": "node scripts/generate-multilingual-sitemap.js", "test-sitemap": "node scripts/test-sitemap.js", "fix-sitemap": "node scripts/fix-sitemap.js"}, "dependencies": {"@next/third-parties": "15.1.2", "date-fns": "^4.1.0", "date-fns-tz": "^3.1.3", "framer-motion": "^12.6.3", "github-slugger": "^2.0.0", "gray-matter": "^4.0.3", "marked": "^15.0.4", "next": "15.0.4", "next-intl": "^4.1.0", "next-mdx-remote": "^5.0.0", "next-sitemap": "^4.2.3", "next-themes": "^0.4.4", "performance-now": "^2.1.0", "postcss": "^8.4.49", "postcss-preset-env": "^9.3.0", "react": "19.0.0", "react-dom": "19.0.0", "react-gravatar": "^2.6.3", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-use-cookie": "^1.6.1", "rehype-slug": "^6.0.0", "remark-gfm": "4.0.0", "sitemap": "^8.0.0", "swiper": "^11.2.6", "tailwind-scrollbar": "^3.1.0"}, "devDependencies": {"@headlessui/react": "^2.2.1", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.31.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "22.10.1", "@types/react": "19.1.2", "app-store-scraper": "^0.18.0", "google-play-scraper": "^10.0.1", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "axios-rate-limit": "^1.4.0", "cssnano": "^6.0.5", "dotenv": "^16.5.0", "eslint": "9.16.0", "eslint-config-next": "15.0.4", "fs-extra": "^11.2.0", "js-yaml": "^4.1.0", "ora": "^8.2.0", "parse-github-url": "^1.0.3", "postcss-flexbugs-fixes": "^5.0.2", "postcss-normalize": "^10.0.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "sass": "^1.89.0", "tailwind-bootstrap-grid": "^5.1.0", "tailwindcss": "^3.4.16", "typescript": "5.7.2", "yaml-front-matter": "^4.1.1", "motion": "^11.15.0"}, "browser": {"fs": false, "os": false, "path": false}}